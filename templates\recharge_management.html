<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值记录管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .search-card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .table-card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .btn-refund {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        
        .btn-refund:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
        }
        
        .btn-reprint {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        
        .btn-reprint:hover {
            background-color: #218838;
            border-color: #1e7e34;
            color: white;
        }
        
        .status-badge {
            font-size: 0.8em;
        }
        
        .amount-text {
            font-weight: bold;
            color: #28a745;
        }
        
        .refund-amount {
            color: #dc3545;
        }
    </style>
</head><
body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-credit-card me-2"></i>充值记录管理</h1>
                    <p class="mb-0">管理客户充值记录，支持退充和补打印功能</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 搜索筛选区域 -->
        <div class="card search-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>搜索筛选</h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">客户姓名</label>
                            <input type="text" class="form-control" id="customerName" placeholder="输入客户姓名">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">客户手机</label>
                            <input type="text" class="form-control" id="customerPhone" placeholder="输入手机号">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">操作员</label>
                            <input type="text" class="form-control" id="operator" placeholder="操作员">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" onclick="searchRechargeRecords()">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button type="button" class="btn btn-success ms-2" onclick="exportRecords()">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>    
    <!-- 充值记录表格 -->
        <div class="card table-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>充值记录</h5>
                <div>
                    <span class="badge bg-info" id="totalCount">总计: 0 条</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>客户信息</th>
                                <th>充值金额</th>
                                <th>赠送金额</th>
                                <th>支付方式</th>
                                <th>充值时间</th>
                                <th>操作员</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="rechargeTableBody">
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="分页导航" id="paginationNav" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 退充确认模态框 -->
    <div class="modal fade" id="refundModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-undo me-2 text-danger"></i>充值退充
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="refundInfo" class="mb-3">
                        <!-- 退充信息将在这里显示 -->
                    </div>
                    <form id="refundForm">
                        <input type="hidden" id="refundRechargeId">
                        <div class="mb-3">
                            <label class="form-label">退充金额 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control" id="refundAmount" 
                                       step="0.01" min="0" required>
                            </div>
                            <div class="form-text">最大可退充金额: ¥<span id="maxRefundAmount">0.00</span></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">退充原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="refundReason" rows="3" 
                                      placeholder="请输入退充原因" required></textarea>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="printRefundReceipt" checked>
                            <label class="form-check-label" for="printRefundReceipt">
                                自动打印退充小票
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmRefundBtn" onclick="executeRefund()">
                        <i class="fas fa-check me-1"></i>确认退充
                    </button>
                </div>
            </div>
        </div>
    </div>  
  <!-- 补打印确认模态框 -->
    <div class="modal fade" id="reprintModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-print me-2 text-success"></i>小票补打印
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reprintInfo" class="mb-3">
                        <!-- 补打印信息将在这里显示 -->
                    </div>
                    <form id="reprintForm">
                        <input type="hidden" id="reprintRecordId">
                        <input type="hidden" id="reprintRecordType">
                        <div class="mb-3">
                            <label class="form-label">补打印原因</label>
                            <input type="text" class="form-control" id="reprintReason" 
                                   placeholder="请输入补打印原因" value="客户要求补打印">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirmReprintBtn" onclick="executeReprint()">
                        <i class="fas fa-print me-1"></i>确认补打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 退充记录查看模态框 -->
    <div class="modal fade" id="refundHistoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history me-2"></i>退充记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>退充时间</th>
                                    <th>退充金额</th>
                                    <th>退充原因</th>
                                    <th>操作员</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="refundHistoryTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script language="javascript" src="/static/js/LodopFuncs.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    <script src="/static/js/refund-print.js"></script>   
 <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentSearchParams = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期范围（最近7天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 加载充值记录
            searchRechargeRecords();
        });

        // 搜索充值记录
        async function searchRechargeRecords(page = 1) {
            try {
                currentPage = page;
                
                // 构建搜索参数
                const params = new URLSearchParams({
                    page: page,
                    per_page: 20
                });
                
                const customerName = document.getElementById('customerName').value.trim();
                const customerPhone = document.getElementById('customerPhone').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const operator = document.getElementById('operator').value.trim();
                
                if (customerName) params.append('customer_name', customerName);
                if (customerPhone) params.append('customer_phone', customerPhone);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (operator) params.append('operator', operator);
                
                currentSearchParams = Object.fromEntries(params);
                
                // 显示加载状态
                showLoading();
                
                const response = await fetch(`/api/recharge_details?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displayRechargeRecords(result.recharge_details);
                    updatePagination(result.current_page, result.total_pages, result.total);
                } else {
                    showError('加载充值记录失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 显示充值记录
        function displayRechargeRecords(records) {
            const tbody = document.getElementById('rechargeTableBody');
            
            if (!records || records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无充值记录</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = records.map(record => `
                <tr>
                    <td>${record.id}</td>
                    <td>
                        <div><strong>${record.customer_name}</strong></div>
                        <small class="text-muted">${record.customer_phone}</small>
                    </td>
                    <td><span class="amount-text">¥${record.amount.toFixed(2)}</span></td>
                    <td><span class="text-success">¥${record.gift_amount.toFixed(2)}</span></td>
                    <td><span class="badge bg-info">${record.payment_method}</span></td>
                    <td><small>${new Date(record.created_at).toLocaleString()}</small></td>
                    <td><span class="badge bg-secondary">${record.operator}</span></td>
                    <td>
                        <span class="badge bg-success">正常</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-refund btn-sm" onclick="showRefundModal(${record.id})" 
                                    title="退充">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-reprint btn-sm" onclick="showReprintModal(${record.id}, 'recharge')" 
                                    title="补打印">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showRefundHistory(${record.id})" 
                                    title="退充记录">
                                <i class="fas fa-history"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }        
// 显示退充模态框
        async function showRefundModal(rechargeId) {
            try {
                // 获取可退充信息
                const response = await fetch(`/api/recharge/${rechargeId}/refundable`);
                const result = await response.json();
                
                if (!result.success) {
                    showAlert('获取退充信息失败: ' + result.error, 'danger');
                    return;
                }
                
                // 填充退充信息
                document.getElementById('refundRechargeId').value = rechargeId;
                document.getElementById('maxRefundAmount').textContent = result.refundable_amount.toFixed(2);
                document.getElementById('refundAmount').max = result.refundable_amount;
                
                const refundInfo = document.getElementById('refundInfo');
                refundInfo.innerHTML = `
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>退充信息</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">可退充金额:</small><br>
                                <strong class="text-success">¥${result.refundable_amount.toFixed(2)}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">当前充值余额:</small><br>
                                <strong>¥${result.current_balance.toFixed(2)}</strong>
                            </div>
                        </div>
                        ${result.current_gift_balance > 0 ? `
                            <div class="row mt-2">
                                <div class="col-6">
                                    <small class="text-muted">当前赠送余额:</small><br>
                                    <strong class="text-info">¥${result.current_gift_balance.toFixed(2)}</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">总余额:</small><br>
                                    <strong class="text-primary">¥${(result.current_balance + result.current_gift_balance).toFixed(2)}</strong>
                                </div>
                            </div>
                        ` : ''}
                        ${result.gift_amount_to_deduct > 0 ? `
                            <div class="mt-2">
                                <small class="text-muted">将扣除赠送金额: </small>
                                <span class="text-warning">¥${result.gift_amount_to_deduct.toFixed(2)}</span>
                            </div>
                        ` : ''}
                        ${!result.can_refund ? `
                            <div class="alert alert-warning mt-2 mb-0">
                                <small><i class="fas fa-exclamation-triangle me-1"></i>${result.reason}</small>
                            </div>
                        ` : ''}
                    </div>
                `;
                
                // 重置表单
                document.getElementById('refundForm').reset();
                document.getElementById('refundRechargeId').value = rechargeId;
                
                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('refundModal'));
                modal.show();
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            }
        }

        // 执行退充操作
        async function executeRefund() {
            try {
                const rechargeId = document.getElementById('refundRechargeId').value;
                const refundAmount = parseFloat(document.getElementById('refundAmount').value);
                const refundReason = document.getElementById('refundReason').value.trim();
                const printReceipt = document.getElementById('printRefundReceipt').checked;
                
                if (!refundAmount || refundAmount <= 0) {
                    showAlert('请输入有效的退充金额', 'warning');
                    return;
                }
                
                if (!refundReason) {
                    showAlert('请输入退充原因', 'warning');
                    return;
                }
                
                // 禁用按钮，显示加载状态
                const btn = document.getElementById('confirmRefundBtn');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
                
                const response = await fetch(`/api/recharge/${rechargeId}/refund`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refund_amount: refundAmount,
                        refund_reason: refundReason,
                        print_receipt: printReceipt
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`退充成功！退充ID: ${result.refund_id}`, 'success');
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('refundModal'));
                    modal.hide();
                    
                    // 刷新列表
                    searchRechargeRecords(currentPage);
                } else {
                    showAlert('退充失败: ' + result.error, 'danger');
                }
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('confirmRefundBtn');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>确认退充';
            }
        }     
   // 显示补打印模态框
        function showReprintModal(recordId, recordType) {
            document.getElementById('reprintRecordId').value = recordId;
            document.getElementById('reprintRecordType').value = recordType;
            
            const reprintInfo = document.getElementById('reprintInfo');
            reprintInfo.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>补打印信息</h6>
                    <p class="mb-0">
                        记录类型: <span class="badge bg-primary">${recordType === 'recharge' ? '充值小票' : '退充小票'}</span><br>
                        记录ID: <strong>${recordId}</strong>
                    </p>
                </div>
            `;
            
            // 重置表单
            document.getElementById('reprintForm').reset();
            document.getElementById('reprintReason').value = '客户要求补打印';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('reprintModal'));
            modal.show();
        }

        // 执行补打印操作
        async function executeReprint() {
            try {
                const recordId = document.getElementById('reprintRecordId').value;
                const recordType = document.getElementById('reprintRecordType').value;
                const reprintReason = document.getElementById('reprintReason').value.trim();
                
                // 禁用按钮，显示加载状态
                const btn = document.getElementById('confirmReprintBtn');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
                
                let apiUrl;
                if (recordType === 'recharge') {
                    apiUrl = `/api/recharge/${recordId}/reprint`;
                } else {
                    apiUrl = `/api/recharge/refund/${recordId}/reprint`;
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reprint_reason: reprintReason
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`补打印成功！记录ID: ${result.reprint_id}`, 'success');
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('reprintModal'));
                    modal.hide();
                    
                    // 调用打印功能 - 使用新的模式选择接口
                    if (recordType === 'recharge') {
                        try {
                            // 优先使用新的模式选择接口
                            if (typeof window.reprintRechargeReceiptWithModeSelection === 'function') {
                                console.log('使用新的充值小票补打印接口');
                                window.reprintRechargeReceiptWithModeSelection(recordId, reprintReason);
                            } else if (typeof window.reprintRechargeReceipt === 'function') {
                                console.log('使用兼容的充值小票补打印接口');
                                window.reprintRechargeReceipt(recordId, reprintReason);
                            } else {
                                throw new Error('充值小票补打印函数不可用');
                            }
                        } catch (printError) {
                            console.error('调用充值补打印函数失败:', printError);
                            showAlert('充值小票打印失败: ' + printError.message, 'warning');
                        }
                    } else if (recordType === 'refund') {
                        try {
                            // 优先使用新的模式选择接口
                            if (typeof window.reprintRefundReceiptWithModeSelection === 'function') {
                                console.log('使用新的退充小票补打印接口');
                                window.reprintRefundReceiptWithModeSelection(recordId, reprintReason);
                            } else if (typeof window.reprintRefundReceipt === 'function') {
                                console.log('使用兼容的退充小票补打印接口');
                                window.reprintRefundReceipt(recordId, reprintReason);
                            } else {
                                throw new Error('退充小票补打印函数不可用');
                            }
                        } catch (printError) {
                            console.error('调用退充补打印函数失败:', printError);
                            showAlert('退充小票打印失败: ' + printError.message, 'warning');
                        }
                    } else {
                        console.error('未知的补打印类型:', recordType);
                        showAlert('未知的补打印类型，请检查参数', 'warning');
                    }
                } else {
                    showAlert('补打印失败: ' + result.error, 'danger');
                }
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('confirmReprintBtn');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-print me-1"></i>确认补打印';
            }
        }

        // 显示退充记录
        async function showRefundHistory(rechargeId) {
            try {
                const response = await fetch(`/api/recharge/refunds?original_recharge_id=${rechargeId}`);
                const result = await response.json();
                
                const tbody = document.getElementById('refundHistoryTableBody');
                
                if (result.success && result.refunds.length > 0) {
                    tbody.innerHTML = result.refunds.map(refund => `
                        <tr>
                            <td><small>${new Date(refund.created_at).toLocaleString()}</small></td>
                            <td><span class="refund-amount">¥${refund.refund_amount.toFixed(2)}</span></td>
                            <td><small>${refund.refund_reason || '无'}</small></td>
                            <td><span class="badge bg-secondary">${refund.operator}</span></td>
                            <td><span class="badge bg-success">${refund.status}</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="showReprintModal(${refund.id}, 'refund')" title="补打印退充小票">
                                    <i class="fas fa-print"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="fas fa-inbox"></i> 暂无退充记录
                            </td>
                        </tr>
                    `;
                }
                
                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('refundHistoryModal'));
                modal.show();
                
            } catch (error) {
                showAlert('获取退充记录失败: ' + error.message, 'danger');
            }
        }  
      // 更新分页
        function updatePagination(currentPage, totalPages, totalCount) {
            document.getElementById('totalCount').textContent = `总计: ${totalCount} 条`;
            
            const paginationNav = document.getElementById('paginationNav');
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                paginationNav.style.display = 'none';
                return;
            }
            
            paginationNav.style.display = 'block';
            
            let paginationHTML = '';
            
            // 上一页
            if (currentPage > 1) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="searchRechargeRecords(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }
            
            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="searchRechargeRecords(${i})">${i}</a>
                    </li>
                `;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="searchRechargeRecords(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }
            
            pagination.innerHTML = paginationHTML;
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchForm').reset();
            
            // 重新设置默认日期
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 重新搜索
            searchRechargeRecords(1);
        }

        // 导出记录
        function exportRecords() {
            const params = new URLSearchParams(currentSearchParams);
            params.delete('page');
            params.delete('per_page');
            
            const url = `/api/export_recharge_details?${params}`;
            window.open(url, '_blank');
        }

        // 显示加载状态
        function showLoading() {
            const tbody = document.getElementById('rechargeTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }

        // 显示错误信息
        function showError(message) {
            const tbody = document.getElementById('rechargeTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>${message}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="searchRechargeRecords(currentPage)">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                    </td>
                </tr>
            `;
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>